{"pattern": {"panels": {"top_back": {"translation": [-0.17666666666666703, 9.85, -15.51], "edges": [{"endpoints": [4, 3]}, {"endpoints": [3, 2]}, {"endpoints": [2, 1], "curvature": [0.3, 0.4]}, {"endpoints": [1, 0]}, {"endpoints": [0, 7], "curvature": [0.5, 0.329020866890147]}, {"endpoints": [7, 6]}, {"endpoints": [6, 5], "curvature": [0.7, 0.4]}, {"endpoints": [5, 4]}], "rotation": [180.0, -0.0, 180.0], "vertices": [[7.781493597825383, 19.583333333333332], [20.920540886095278, 19.583333333333332], [24.724275592658053, -10.542059549381978], [18.466453891546347, -17.08333333333333], [-18.466453891546347, -17.08333333333333], [-24.724275592658053, -10.542059549381978], [-20.920540886095278, 19.583333333333332], [-7.781493597825383, 19.583333333333332]]}, "top_front": {"translation": [0.17666666666666703, 10.743979826009564, 18.759999999999998], "edges": [{"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7], "curvature": [0.3, 0.4]}, {"endpoints": [7, 8]}, {"endpoints": [8, 0]}, {"endpoints": [0, 1]}, {"endpoints": [1, 2]}, {"endpoints": [2, 3], "curvature": [0.7, 0.4]}, {"endpoints": [3, 4]}], "rotation": [0.0, 0.0, 0.0], "vertices": [[0.0, -1.56623663683947], [-7.781493597825383, 18.703703703703702], [-20.920540886095278, 18.703703703703702], [-24.724275592658053, -11.4216891790116], [-18.466453891546347, -17.96296296296296], [18.466453891546347, -17.96296296296296], [24.724275592658053, -11.4216891790116], [20.920540886095278, 18.703703703703702], [7.781493597825383, 18.703703703703702]]}, "skirt_front": {"translation": [0.17666666666666703, -40.00133333333333, 18.759999999999998], "edges": [{"endpoints": [4, 3], "curvature": [0.5, 0.08416497918780985]}, {"endpoints": [3, 2]}, {"endpoints": [2, 1]}, {"endpoints": [1, 0]}, {"endpoints": [0, 5]}, {"endpoints": [5, 4]}], "rotation": [0.0, 0.0, 0.0], "vertices": [[-18.466453891546347, 24.999999999999993], [18.466453891546347, 24.999999999999993], [23.303742688024506, 10.0], [33.45336052826443, -19.959005837394905], [-33.45336052826443, -19.959005837394905], [-23.303742688024506, 10.0]]}, "skirt_back": {"translation": [-0.17666666666666703, -40.00133333333333, -15.51], "edges": [{"endpoints": [3, 4], "curvature": [0.5, -0.0010959458760045671]}, {"endpoints": [4, 5]}, {"endpoints": [5, 0]}, {"endpoints": [0, 1]}, {"endpoints": [1, 2]}, {"endpoints": [2, 3]}], "rotation": [180.0, -0.0, 180.0], "vertices": [[18.466453891546347, 24.999999999999993], [-18.466453891546347, 24.999999999999993], [-23.303742688024506, 10.0], [-33.45336052826443, -19.959005837394905], [33.45336052826443, -19.959005837394905], [23.303742688024506, 10.0]]}, "lfsleeve": {"translation": [38.51169502955079, 20.666666666666664, 12.146537531143998], "edges": [{"endpoints": [3, 2]}, {"endpoints": [2, 1], "curvature": [0.7, -0.4]}, {"endpoints": [1, 0]}, {"endpoints": [0, 3]}], "rotation": [180.0, 180, 0.0], "vertices": [[-31.74011052271327, 13.542950618586637], [5.0, 21.792059549381978], [8.333333333333332, -8.333333333333332], [-31.74011052271327, -8.333333333333332]]}, "lbsleeve": {"translation": [38.5, 20.666666666666664, -11.721142051745986], "edges": [{"endpoints": [3, 2]}, {"endpoints": [2, 1], "curvature": [0.3, -0.4]}, {"endpoints": [1, 0]}, {"endpoints": [0, 3]}], "rotation": [0.0, 180, 0.0], "vertices": [[-31.74011052271327, 8.333333333333332], [8.333333333333332, 8.333333333333332], [5.0, -21.792059549381978], [-31.74011052271327, -13.542950618586637]]}, "rbsleeve": {"translation": [-39.42419743913558, 20.666666666666664, -11.721142051745986], "edges": [{"endpoints": [3, 2]}, {"endpoints": [2, 1], "curvature": [0.7, -0.4]}, {"endpoints": [1, 0]}, {"endpoints": [0, 3]}], "rotation": [180.0, 0.0, 0.0], "vertices": [[-31.74011052271327, 13.542950618586637], [5.0, 21.792059549381978], [8.333333333333332, -8.333333333333332], [-31.74011052271327, -8.333333333333332]]}, "rfsleeve": {"translation": [-39.43333333333333, 20.666666666666664, 12.133333333333333], "edges": [{"endpoints": [3, 2]}, {"endpoints": [2, 1], "curvature": [0.3, -0.4]}, {"endpoints": [1, 0]}, {"endpoints": [0, 3]}], "rotation": [0.0, 0.0, 0.0], "vertices": [[-31.74011052271327, 8.333333333333332], [8.333333333333332, 8.333333333333332], [5.0, -21.792059549381978], [-31.74011052271327, -13.542950618586637]]}}, "stitches": [[{"edge": 7, "panel": "top_back"}, {"edge": 1, "panel": "top_front"}], [{"edge": 5, "panel": "top_back"}, {"edge": 3, "panel": "top_front"}], [{"edge": 3, "panel": "top_back"}, {"edge": 6, "panel": "top_front"}], [{"edge": 1, "panel": "top_back"}, {"edge": 8, "panel": "top_front"}], [{"edge": 2, "panel": "skirt_back"}, {"edge": 4, "panel": "skirt_front"}], [{"edge": 1, "panel": "skirt_back"}, {"edge": 5, "panel": "skirt_front"}], [{"edge": 5, "panel": "skirt_back"}, {"edge": 1, "panel": "skirt_front"}], [{"edge": 4, "panel": "skirt_back"}, {"edge": 2, "panel": "skirt_front"}], [{"edge": 3, "panel": "skirt_front"}, {"edge": 0, "panel": "top_front"}], [{"edge": 0, "panel": "top_back"}, {"edge": 3, "panel": "skirt_back"}], [{"edge": 1, "panel": "rfsleeve"}, {"edge": 7, "panel": "top_front"}], [{"edge": 1, "panel": "rbsleeve"}, {"edge": 2, "panel": "top_back"}], [{"edge": 1, "panel": "lfsleeve"}, {"edge": 2, "panel": "top_front"}], [{"edge": 1, "panel": "lbsleeve"}, {"edge": 6, "panel": "top_back"}], [{"edge": 0, "panel": "lbsleeve"}, {"edge": 2, "panel": "lfsleeve"}], [{"edge": 2, "panel": "lbsleeve"}, {"edge": 0, "panel": "lfsleeve"}], [{"edge": 2, "panel": "rbsleeve"}, {"edge": 0, "panel": "rfsleeve"}], [{"edge": 0, "panel": "rbsleeve"}, {"edge": 2, "panel": "rfsleeve"}]], "panel_order": ["rbsleeve", "rfsleeve", "skirt_back", "skirt_front", "top_back", "top_front", "lbsleeve", "lfsleeve"]}, "properties": {"curvature_coords": "relative", "normalize_panel_translation": false, "units_in_meter": 100, "normalized_edge_loops": true}, "parameters": {"waist_wideness": {"type": "length", "range": [0.9, 1.3], "influence": [{"panel": "skirt_front", "edge_list": [{"direction": "both", "id": 3}]}, {"panel": "skirt_back", "edge_list": [{"direction": "both", "id": 3}]}, {"panel": "top_front", "edge_list": [{"direction": "both", "id": 0}]}, {"panel": "top_back", "edge_list": [{"direction": "both", "id": 0}]}], "value": 1.0526814090625227}, "collar_wide": {"type": "length", "range": [0.5, 1.5], "influence": [{"panel": "top_front", "edge_list": [{"direction": "both", "id": [4, 5]}]}, {"panel": "top_back", "edge_list": [{"direction": "both", "id": 4}]}], "value": 0.8183003493276835}, "bcollar_depth": {"type": "curve", "range": [0.1, 7], "influence": [{"panel": "top_back", "edge_list": [4]}], "value": 1.0967362229671567}, "wideness": {"type": "length", "range": [1, 1.3], "influence": [{"panel": "top_front", "edge_list": [{"direction": "both", "id": [2, 3, 4, 5, 6, 7]}, {"direction": "both", "id": 0}]}, {"panel": "top_back", "edge_list": [{"direction": "both", "id": [2, 3, 4, 5, 6]}, {"direction": "both", "id": 0}]}, {"panel": "skirt_back", "edge_list": [{"direction": "both", "id": [2, 3, 4]}]}, {"panel": "skirt_front", "edge_list": [{"direction": "both", "id": [2, 3, 4]}]}], "value": 1.0319000452261775}, "top_wideness": {"type": "length", "range": [0.9, 1.3], "influence": [{"panel": "top_front", "edge_list": [{"direction": "both", "id": [2, 3, 4, 5, 6, 7]}]}, {"panel": "top_back", "edge_list": [{"direction": "both", "id": [2, 3, 4, 5, 6]}]}], "value": 1.1058439402614004}, "fcollar_depth": {"type": "length", "range": [0.1, 2.5], "influence": [{"panel": "top_front", "edge_list": [{"direction": "start", "along": [0, 1], "id": 5}]}], "value": 1.7374234577608443}, "sldepth": {"type": "length", "range": [0.85, 1.9], "influence": [{"panel": "lbsleeve", "edge_list": [{"along": [0, 1], "direction": "start", "id": 1}]}, {"panel": "lfsleeve", "edge_list": [{"direction": "end", "along": [0, 1], "id": 1}]}, {"panel": "rfsleeve", "edge_list": [{"direction": "start", "along": [0, 1], "id": 1}]}, {"panel": "rbsleeve", "edge_list": [{"direction": "end", "along": [0, 1], "id": 1}]}, {"panel": "top_front", "edge_list": [{"direction": "end", "along": [0, 1], "id": 7}, {"direction": "start", "along": [0, 1], "id": 2}]}, {"panel": "top_back", "edge_list": [{"direction": "end", "along": [0, 1], "id": 6}, {"direction": "start", "along": [0, 1], "id": 2}]}], "value": 1.8075235729629189}, "skirt_wideness": {"type": "length", "range": [0.65, 1.8], "influence": [{"panel": "skirt_front", "edge_list": [{"direction": "both", "id": 0}]}, {"panel": "skirt_back", "edge_list": [{"direction": "both", "id": 0}]}], "value": 1.0855962194695892}, "skirt_length": {"type": "length", "range": [0.45, 1.9], "influence": [{"panel": "skirt_front", "edge_list": [{"direction": "end", "id": 5}, {"direction": "start", "id": 1}]}, {"panel": "skirt_back", "edge_list": [{"direction": "end", "id": 5}, {"direction": "start", "id": 1}]}], "value": 0.7489751459348726}, "curve_front": {"type": "curve", "range": [-5, 5], "influence": [{"panel": "skirt_front", "edge_list": [0]}], "value": -0.8416497918780985}, "curve_back": {"type": "curve", "range": [-5, 5], "influence": [{"panel": "skirt_back", "edge_list": [0]}], "value": 0.01095945876004567}, "sllength": {"type": "additive_length", "range": [-6.666666666666666, 36.666666666666664], "influence": [{"panel": "lbsleeve", "edge_list": [{"direction": "end", "along": [1, 0], "id": 2}, {"direction": "start", "along": [1, 0], "id": 0}]}, {"panel": "lfsleeve", "edge_list": [{"direction": "end", "along": [1, 0], "id": 2}, {"direction": "start", "along": [1, 0], "id": 0}]}, {"panel": "rfsleeve", "edge_list": [{"direction": "start", "along": [1, 0], "id": 0}, {"direction": "end", "along": [1, 0], "id": 2}]}, {"panel": "rbsleeve", "edge_list": [{"direction": "start", "along": [1, 0], "id": 0}, {"direction": "end", "along": [1, 0], "id": 2}]}], "value": 28.406777189379937}, "send_wide": {"type": "length", "range": [1, 3.5], "influence": [{"panel": "lbsleeve", "edge_list": [{"direction": "end", "id": 3}]}, {"panel": "lfsleeve", "edge_list": [{"direction": "start", "id": 3}]}, {"panel": "rfsleeve", "edge_list": [{"direction": "end", "id": 3}]}, {"panel": "rbsleeve", "edge_list": [{"direction": "start", "id": 3}]}], "value": 1.3125770371151984}}, "parameter_order": ["waist_wideness", "collar_wide", "fcollar_depth", "bcollar_depth", "top_wideness", "wideness", "send_wide", "sllength", "sldepth", "skirt_length", "skirt_wideness", "curve_front", "curve_back"]}