{"pattern": {"panels": {"left_ftorso": {"translation": [0.0, 100.63745745586257, 30.0], "rotation": [-0.0, 0.0, 0.0], "vertices": [[0.0, 0.0], [8.42655012651451, 0.0], [9.43568345984784, 13.496556287010977], [10.444816793181177, 2.4716638131591317e-16], [24.586366666666667, 0.0], [25.5955, 15.03803352158066], [12.844786470743832, 16.834983521580657], [25.5955, 18.631933521580656], [24.091332478807033, 23.72573324376787], [17.723032775980013, 41.90525324376787], [11.12785, 44.6345994954509], [11.12785, 20.370706315522824], [0.0, 20.370706315522824]], "edges": [{"endpoints": [0, 1]}, {"endpoints": [1, 2]}, {"endpoints": [2, 3]}, {"endpoints": [3, 4]}, {"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7]}, {"endpoints": [7, 8]}, {"endpoints": [8, 9], "label": "left_armhole", "curvature": {"type": "cubic", "params": [[0.19999999999999996, 0.35], [0.5, 0.2]]}}, {"endpoints": [9, 10]}, {"endpoints": [10, 11], "label": "left_collar"}, {"endpoints": [11, 12], "label": "left_collar"}, {"endpoints": [12, 0]}], "label": "body"}, "left_btorso": {"translation": [0.0, 106.74136076913945, -25.0], "rotation": [-0.0, 0.0, 0.0], "vertices": [[0.0, 0.5100033132768815], [0.0, 33.605829340842625], [11.12785, 39.04069963699753], [17.86225, 36.25373923816242], [24.26065, 18.074219238162417], [24.26065, 13.038033333333331], [23.442308333333333, -2.0], [15.347436562064892, -0.48997940087440295], [15.722833015469504, 11.027470803077183], [13.316656461886222, -0.2420853590225145], [8.817157162575288, 0.18103159061304952], [8.637993943750201, 12.983740656553374], [6.775755012988105, 0.315926123223468]], "edges": [{"endpoints": [0, 1]}, {"endpoints": [1, 2], "label": "left_collar"}, {"endpoints": [2, 3]}, {"endpoints": [3, 4], "label": "left_armhole", "curvature": {"type": "cubic", "params": [[0.5, -0.2], [0.8, -0.35]]}}, {"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7], "curvature": {"type": "quadratic", "params": [[0.5020582705220284, -0.018803508254212613]]}}, {"endpoints": [7, 8]}, {"endpoints": [8, 9]}, {"endpoints": [9, 10], "curvature": {"type": "quadratic", "params": [[0.5003341576735687, -0.0095520887914988]]}}, {"endpoints": [10, 11]}, {"endpoints": [11, 12]}, {"endpoints": [12, 0], "curvature": {"type": "quadratic", "params": [[0.4995675999184962, -0.01433383334366459]]}}], "label": "body"}, "right_ftorso": {"translation": [0.0, 100.63745745586257, 30.0], "rotation": [-0.0, 0.0, 0.0], "vertices": [[0, 0], [0.0, 20.370706315522824], [-11.12785, 20.370706315522824], [-11.12785, 44.6345994954509], [-17.723032775980013, 41.90525324376787], [-24.091332478807033, 23.72573324376787], [-25.5955, 18.631933521580656], [-12.844786470743832, 16.834983521580657], [-25.5955, 15.03803352158066], [-24.586366666666667, 0], [-10.444816793181177, 2.4716638131591317e-16], [-9.43568345984784, 13.496556287010977], [-8.42655012651451, 0.0]], "edges": [{"endpoints": [0, 1]}, {"endpoints": [1, 2], "label": "right_collar"}, {"endpoints": [2, 3], "label": "right_collar"}, {"endpoints": [3, 4]}, {"endpoints": [4, 5], "label": "right_armhole", "curvature": {"type": "cubic", "params": [[0.5, 0.2], [0.8, 0.35]]}}, {"endpoints": [5, 6]}, {"endpoints": [6, 7]}, {"endpoints": [7, 8]}, {"endpoints": [8, 9]}, {"endpoints": [9, 10]}, {"endpoints": [10, 11]}, {"endpoints": [11, 12]}, {"endpoints": [12, 0]}], "label": "body"}, "right_btorso": {"translation": [0.0, 106.74136076913945, -25.0], "rotation": [-0.0, 0.0, 0.0], "vertices": [[0, 0.5100033132768815], [-6.775755012988105, 0.315926123223468], [-8.637993943750201, 12.983740656553374], [-8.817157162575288, 0.18103159061304952], [-13.316656461886222, -0.2420853590225145], [-15.722833015469504, 11.027470803077183], [-15.347436562064892, -0.48997940087440295], [-23.442308333333333, -2], [-24.26065, 13.038033333333331], [-24.26065, 18.074219238162417], [-17.86225, 36.25373923816242], [-11.12785, 39.04069963699753], [0.0, 33.605829340842625]], "edges": [{"endpoints": [0, 1], "curvature": {"type": "quadratic", "params": [[0.5004324000815038, -0.01433383334366459]]}}, {"endpoints": [1, 2]}, {"endpoints": [2, 3]}, {"endpoints": [3, 4], "curvature": {"type": "quadratic", "params": [[0.49966584232643135, -0.0095520887914988]]}}, {"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7], "curvature": {"type": "quadratic", "params": [[0.4979417294779716, -0.018803508254212613]]}}, {"endpoints": [7, 8]}, {"endpoints": [8, 9]}, {"endpoints": [9, 10], "label": "right_armhole", "curvature": {"type": "cubic", "params": [[0.19999999999999996, -0.35], [0.5, -0.2]]}}, {"endpoints": [10, 11]}, {"endpoints": [11, 12], "label": "right_collar"}, {"endpoints": [12, 0]}], "label": "body"}}, "stitches": [[{"panel": "left_ftorso", "edge": 5}, {"panel": "left_ftorso", "edge": 6}], [{"panel": "left_ftorso", "edge": 1}, {"panel": "left_ftorso", "edge": 2}], [{"panel": "left_btorso", "edge": 8}, {"panel": "left_btorso", "edge": 7}], [{"panel": "left_btorso", "edge": 11}, {"panel": "left_btorso", "edge": 10}], [{"panel": "left_ftorso", "edge": 9}, {"panel": "left_btorso", "edge": 2}], [{"panel": "left_ftorso", "edge": 4}, {"panel": "left_btorso", "edge": 5}], [{"panel": "left_ftorso", "edge": 7}, {"panel": "left_btorso", "edge": 4}], [{"panel": "right_ftorso", "edge": 7}, {"panel": "right_ftorso", "edge": 6}], [{"panel": "right_ftorso", "edge": 11}, {"panel": "right_ftorso", "edge": 10}], [{"panel": "right_btorso", "edge": 4}, {"panel": "right_btorso", "edge": 5}], [{"panel": "right_btorso", "edge": 1}, {"panel": "right_btorso", "edge": 2}], [{"panel": "right_ftorso", "edge": 3}, {"panel": "right_btorso", "edge": 10}], [{"panel": "right_ftorso", "edge": 8}, {"panel": "right_btorso", "edge": 7}], [{"panel": "right_ftorso", "edge": 5}, {"panel": "right_btorso", "edge": 8}], [{"panel": "right_ftorso", "edge": 0}, {"panel": "left_ftorso", "edge": 12}], [{"panel": "right_btorso", "edge": 12}, {"panel": "left_btorso", "edge": 0}]], "panel_order": ["right_btorso", "right_ftorso", "left_btorso", "left_ftorso"]}, "parameters": {}, "parameter_order": [], "properties": {"curvature_coords": "relative", "normalize_panel_translation": false, "normalized_edge_loops": true, "units_in_meter": 100}}