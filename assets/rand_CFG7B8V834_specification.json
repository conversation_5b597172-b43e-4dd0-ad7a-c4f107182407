{"pattern": {"panels": {"left_ftorso": {"translation": [0.0, 97.85775552378857, 30.0], "rotation": [-0.0, 0.0, 0.0], "vertices": [[0.0, 0.0], [8.473150131373654, 0.0], [9.634250131373655, 12.757752334184888], [10.795350131373658, 2.8438747969799893e-16], [24.921200000000002, 0.0], [26.082300000000004, 14.233866642059992], [13.003468750843743, 16.06836664205999], [26.082300000000004, 17.90286664205999], [24.46108877526564, 22.579840538285193], [17.068593319850624, 41.021320538285195], [10.841336839460238, 43.496639462604605], [0.0, 31.49732656623194]], "edges": [{"endpoints": [0, 1]}, {"endpoints": [1, 2]}, {"endpoints": [2, 3]}, {"endpoints": [3, 4]}, {"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7]}, {"endpoints": [7, 8]}, {"endpoints": [8, 9], "label": "left_armhole", "curvature": {"type": "cubic", "params": [[0.19999999999999996, 0.35], [0.5, 0.2]]}}, {"endpoints": [9, 10]}, {"endpoints": [10, 11], "label": "left_collar", "curvature": {"type": "quadratic", "params": [[0.30000000000000004, 0.55]]}}, {"endpoints": [11, 0]}], "label": "body"}, "left_btorso": {"translation": [0.0, 103.895647186128, -25.0], "rotation": [-0.0, 0.0, 0.0], "vertices": [[0.0, 0.36889166233942916], [0.0, 13.43870953616954], [10.841336839460238, 37.82763965397433], [17.2284, 35.28879738065446], [23.83805, 16.847317380654456], [23.83805, 12.233866666666664], [23.126358333333332, -2.0], [14.916717524757459, -0.5550002787946218], [15.282781625402311, 10.242436235405265], [13.149513068956622, -0.3484940103303784], [8.60976632837318, 0.061960367182088305], [8.486933461521588, 12.06537639301566], [6.834173497888344, 0.175655256461269]], "edges": [{"endpoints": [0, 1]}, {"endpoints": [1, 2], "label": "left_collar", "curvature": {"type": "quadratic", "params": [[0.85, -0.051836584654487566]]}}, {"endpoints": [2, 3]}, {"endpoints": [3, 4], "label": "left_armhole", "curvature": {"type": "cubic", "params": [[0.5, -0.2], [0.8, -0.35]]}}, {"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7], "curvature": {"type": "quadratic", "params": [[0.5019803766939228, -0.01839842016264157]]}}, {"endpoints": [7, 8]}, {"endpoints": [8, 9]}, {"endpoints": [9, 10], "curvature": {"type": "quadratic", "params": [[0.5003470519620727, -0.00942210554185744]]}}, {"endpoints": [10, 11]}, {"endpoints": [11, 12]}, {"endpoints": [12, 0], "curvature": {"type": "quadratic", "params": [[0.49964505281690064, -0.014147551785365786]]}}], "label": "body"}, "right_btorso": {"translation": [0.0, 103.895647186128, -25.0], "rotation": [-0.0, 0.0, 0.0], "vertices": [[0, 0.36889166233942916], [-6.834173497888344, 0.175655256461269], [-8.486933461521588, 12.06537639301566], [-8.60976632837318, 0.061960367182088305], [-13.149513068956622, -0.3484940103303784], [-15.282781625402311, 10.242436235405265], [-14.916717524757459, -0.5550002787946218], [-23.126358333333332, -2], [-23.83805, 12.233866666666664], [-23.83805, 16.847317380654456], [-17.2284, 35.28879738065446], [-10.841336839460238, 37.82763965397433], [0.0, 13.43870953616954]], "edges": [{"endpoints": [0, 1], "curvature": {"type": "quadratic", "params": [[0.5003549471830994, -0.014147551785365786]]}}, {"endpoints": [1, 2]}, {"endpoints": [2, 3]}, {"endpoints": [3, 4], "curvature": {"type": "quadratic", "params": [[0.4996529480379273, -0.00942210554185744]]}}, {"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7], "curvature": {"type": "quadratic", "params": [[0.49801962330607724, -0.01839842016264157]]}}, {"endpoints": [7, 8]}, {"endpoints": [8, 9]}, {"endpoints": [9, 10], "label": "right_armhole", "curvature": {"type": "cubic", "params": [[0.19999999999999996, -0.35], [0.5, -0.2]]}}, {"endpoints": [10, 11]}, {"endpoints": [11, 12], "label": "right_collar", "curvature": {"type": "quadratic", "params": [[0.15000000000000002, -0.051836584654487566]]}}, {"endpoints": [12, 0]}], "label": "body"}, "right_ftorso": {"translation": [0.0, 97.85775552378857, 30.0], "rotation": [-0.0, 0.0, 0.0], "vertices": [[0, 0], [0.0, 31.49732656623194], [-10.841336839460238, 43.496639462604605], [-17.068593319850624, 41.021320538285195], [-24.46108877526564, 22.579840538285193], [-26.082300000000004, 17.90286664205999], [-13.003468750843743, 16.06836664205999], [-26.082300000000004, 14.233866642059992], [-24.921200000000002, 0], [-10.795350131373658, 2.8438747969799893e-16], [-9.634250131373655, 12.757752334184888], [-8.473150131373654, 0.0]], "edges": [{"endpoints": [0, 1]}, {"endpoints": [1, 2], "label": "right_collar", "curvature": {"type": "quadratic", "params": [[0.7, 0.55]]}}, {"endpoints": [2, 3]}, {"endpoints": [3, 4], "label": "right_armhole", "curvature": {"type": "cubic", "params": [[0.5, 0.2], [0.8, 0.35]]}}, {"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7]}, {"endpoints": [7, 8]}, {"endpoints": [8, 9]}, {"endpoints": [9, 10]}, {"endpoints": [10, 11]}, {"endpoints": [11, 0]}], "label": "body"}}, "stitches": [[{"panel": "left_ftorso", "edge": 5}, {"panel": "left_ftorso", "edge": 6}], [{"panel": "left_ftorso", "edge": 1}, {"panel": "left_ftorso", "edge": 2}], [{"panel": "left_btorso", "edge": 8}, {"panel": "left_btorso", "edge": 7}], [{"panel": "left_btorso", "edge": 11}, {"panel": "left_btorso", "edge": 10}], [{"panel": "left_ftorso", "edge": 9}, {"panel": "left_btorso", "edge": 2}], [{"panel": "left_ftorso", "edge": 4}, {"panel": "left_btorso", "edge": 5}], [{"panel": "left_ftorso", "edge": 7}, {"panel": "left_btorso", "edge": 4}], [{"panel": "right_btorso", "edge": 4}, {"panel": "right_btorso", "edge": 5}], [{"panel": "right_btorso", "edge": 1}, {"panel": "right_btorso", "edge": 2}], [{"panel": "right_ftorso", "edge": 6}, {"panel": "right_ftorso", "edge": 5}], [{"panel": "right_ftorso", "edge": 10}, {"panel": "right_ftorso", "edge": 9}], [{"panel": "right_ftorso", "edge": 2}, {"panel": "right_btorso", "edge": 10}], [{"panel": "right_ftorso", "edge": 7}, {"panel": "right_btorso", "edge": 7}], [{"panel": "right_ftorso", "edge": 4}, {"panel": "right_btorso", "edge": 8}], [{"panel": "right_ftorso", "edge": 0}, {"panel": "left_ftorso", "edge": 11}], [{"panel": "right_btorso", "edge": 12}, {"panel": "left_btorso", "edge": 0}]], "panel_order": ["right_btorso", "right_ftorso", "left_btorso", "left_ftorso"]}, "parameters": {}, "parameter_order": [], "properties": {"curvature_coords": "relative", "normalize_panel_translation": false, "normalized_edge_loops": true, "units_in_meter": 100}}