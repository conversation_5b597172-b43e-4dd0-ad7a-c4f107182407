{"pattern": {"panels": {"up_back": {"translation": [-0.17666666666666703, 10.85, -15.51], "edges": [{"endpoints": [5, 4]}, {"endpoints": [4, 3]}, {"endpoints": [3, 2]}, {"endpoints": [2, 1], "curvature": [0.3, 0.4]}, {"endpoints": [1, 0]}, {"endpoints": [0, 8], "curvature": [0.5, 1.271448921241763]}, {"endpoints": [8, 7]}, {"endpoints": [7, 6], "curvature": [0.7, 0.4]}, {"endpoints": [6, 5]}], "rotation": [180.0, -0.0, 180.0], "vertices": [[7.956202877506682, 19.583333333333332], [22.133623602193882, 19.583333333333332], [26.157918802592768, -12.746613297144357], [21.308613512153226, -17.08333333333333], [0.0, -17.08333333333333], [-21.308613512153226, -17.08333333333333], [-26.157918802592768, -12.746613297144357], [-22.133623602193882, 19.583333333333332], [-7.956202877506682, 19.583333333333332]]}, "up_front": {"translation": [0.17666666666666703, 11.743979826009564, 18.759999999999998], "edges": [{"endpoints": [4, 5]}, {"endpoints": [5, 6]}, {"endpoints": [6, 7]}, {"endpoints": [7, 8], "curvature": [0.3, 0.4]}, {"endpoints": [8, 9]}, {"endpoints": [9, 0]}, {"endpoints": [0, 1]}, {"endpoints": [1, 2]}, {"endpoints": [2, 3], "curvature": [0.7, 0.4]}, {"endpoints": [3, 4]}], "rotation": [0.0, 0.0, 0.0], "vertices": [[0.0, 3.6303001851940135], [-7.956202877506682, 18.703703703703702], [-22.133623602193882, 18.703703703703702], [-26.157918802592768, -13.62624292677398], [-21.308613512153226, -17.96296296296296], [0.0, -17.96296296296296], [21.308613512153226, -17.96296296296296], [26.157918802592768, -13.62624292677398], [22.133623602193882, 18.703703703703702], [7.956202877506682, 18.703703703703702]]}, "Lback": {"translation": [32.5, -10, -13.03078279020406], "edges": [{"endpoints": [2, 3]}, {"endpoints": [3, 4], "curvature": [0.95, 0.1]}, {"endpoints": [4, 0], "curvature": [0.25, 0.4]}, {"endpoints": [0, 1]}, {"endpoints": [1, 2], "curvature": [0.15, -0.05]}], "rotation": [0.0, 180.0, 0.0], "vertices": [[21.67, 0.0], [0.3581082396141317, 0.0], [2.706010162254479, -73.43048819478983], [31.67, -73.4307064154479], [31.67, -31.5]]}, "Rback": {"translation": [-32.5, -10, -13.0], "edges": [{"endpoints": [3, 2]}, {"endpoints": [2, 1], "curvature": [0.85, -0.05]}, {"endpoints": [1, 0]}, {"endpoints": [0, 4], "curvature": [0.75, 0.4]}, {"endpoints": [4, 3], "curvature": [0.05, 0.1]}], "rotation": [0, 180, 0], "vertices": [[-21.67, 0.0], [-0.3581082396141317, 0.0], [-2.706010162254479, -73.43048819478983], [-31.67, -73.4307064154479], [-31.67, -31.5]]}, "Rfront": {"translation": [-32.5, -10, 17.110372122518136], "edges": [{"endpoints": [2, 3]}, {"endpoints": [3, 4], "curvature": [0.95, 0.1]}, {"endpoints": [4, 0], "curvature": [0.25, 0.35]}, {"endpoints": [0, 1]}, {"endpoints": [1, 2], "curvature": [0.15, -0.05]}], "rotation": [0.0, 0.0, 0.0], "vertices": [[21.67, 0.0], [0.3581082396141317, 0.0], [2.706010162254479, -73.43048819478983], [31.67, -73.4307064154479], [31.67, -31.5]]}, "Lfront": {"translation": [32.5, -10, 17.1], "edges": [{"endpoints": [3, 2]}, {"endpoints": [2, 1], "curvature": [0.85, -0.05]}, {"endpoints": [1, 0]}, {"endpoints": [0, 4], "curvature": [0.75, 0.35]}, {"endpoints": [4, 3], "curvature": [0.05, 0.1]}], "rotation": [0, 0, 0], "vertices": [[-21.67, 0.0], [-0.3581082396141317, 0.0], [-2.706010162254479, -73.43048819478983], [-31.67, -73.4307064154479], [-31.67, -31.5]]}}, "stitches": [[{"edge": 4, "panel": "up_back"}, {"edge": 7, "panel": "up_front"}], [{"edge": 2, "panel": "up_back"}, {"edge": 9, "panel": "up_front"}], [{"edge": 8, "panel": "up_back"}, {"edge": 2, "panel": "up_front"}], [{"edge": 6, "panel": "up_back"}, {"edge": 4, "panel": "up_front"}], [{"edge": 4, "panel": "Rfront"}, {"edge": 1, "panel": "<PERSON><PERSON>"}], [{"edge": 1, "panel": "Rfront"}, {"edge": 4, "panel": "<PERSON><PERSON>"}], [{"edge": 1, "panel": "Lfront"}, {"edge": 4, "panel": "Lback"}], [{"edge": 4, "panel": "Lfront"}, {"edge": 1, "panel": "Lback"}], [{"edge": 3, "panel": "<PERSON><PERSON>"}, {"edge": 2, "panel": "Lback"}], [{"edge": 2, "panel": "Rfront"}, {"edge": 3, "panel": "Lfront"}], [{"edge": 3, "panel": "Rfront"}, {"edge": 0, "panel": "up_front"}], [{"edge": 2, "panel": "Lfront"}, {"edge": 1, "panel": "up_front"}], [{"edge": 3, "panel": "Lback"}, {"edge": 0, "panel": "up_back"}], [{"edge": 2, "panel": "<PERSON><PERSON>"}, {"edge": 1, "panel": "up_back"}]], "panel_order": ["<PERSON><PERSON>", "Rfront", "up_back", "up_front", "Lback", "Lfront"]}, "properties": {"curvature_coords": "relative", "normalize_panel_translation": false, "units_in_meter": 100, "normalized_edge_loops": true}, "parameters": {"leg_length": {"influence": [{"edge_list": [{"direction": "end", "id": 4}, {"direction": "start", "id": 1}], "panel": "Rfront"}, {"edge_list": [{"direction": "end", "id": 4}, {"direction": "start", "id": 1}], "panel": "Lfront"}, {"edge_list": [{"direction": "end", "id": 4}, {"direction": "start", "id": 1}], "panel": "Lback"}, {"edge_list": [{"direction": "end", "id": 4}, {"direction": "start", "id": 1}], "panel": "<PERSON><PERSON>"}], "range": [-20, 45], "type": "additive_length", "value": 13.4307064154479}, "leg_wideness": {"influence": [{"edge_list": [{"direction": "start", "id": 0}], "panel": "Rfront"}, {"edge_list": [{"direction": "end", "id": 0}], "panel": "Lfront"}, {"edge_list": [{"direction": "start", "id": 0}], "panel": "Lback"}, {"edge_list": [{"direction": "end", "id": 0}], "panel": "<PERSON><PERSON>"}], "range": [-18, 10], "type": "additive_length", "value": -2.786169511789204}, "collar_wide": {"type": "length", "range": [0.5, 1.5], "influence": [{"panel": "up_front", "edge_list": [{"direction": "both", "id": [5, 6]}]}, {"panel": "up_back", "edge_list": [{"direction": "both", "id": 5}]}], "value": 0.7908170232360755}, "bcollar_depth": {"type": "curve", "range": [0.1, 5], "influence": [{"panel": "up_back", "edge_list": [5]}], "value": 4.238163070805877}, "top_wideness": {"type": "length", "range": [0.9, 1.3], "influence": [{"panel": "up_front", "edge_list": [{"direction": "both", "id": [3, 4, 5, 6, 7, 8]}]}, {"panel": "up_back", "edge_list": [{"direction": "both", "id": [3, 4, 5, 6, 7]}]}], "value": 1.2072885601196663}, "waist_wideness": {"type": "length", "range": [0.9, 1.3], "influence": [{"panel": "up_front", "edge_list": [{"direction": "both", "id": [0, 1]}]}, {"panel": "up_back", "edge_list": [{"direction": "both", "id": [0, 1]}]}, {"panel": "Rfront", "edge_list": [{"direction": "end", "id": 3}]}, {"panel": "Lfront", "edge_list": [{"direction": "start", "id": 2}]}, {"panel": "<PERSON><PERSON>", "edge_list": [{"direction": "start", "id": 2}]}, {"panel": "Lback", "edge_list": [{"direction": "end", "id": 3}]}], "value": 0.9834744697916875}, "fcollar_depth": {"type": "length", "range": [0.1, 2.5], "influence": [{"panel": "up_front", "edge_list": [{"direction": "start", "along": [0, 1], "id": 6}]}], "value": 1.2920060158722597}, "sldepth": {"type": "length", "range": [0.85, 2], "influence": [{"panel": "up_front", "edge_list": [{"direction": "end", "along": [0, 1], "id": 8}, {"direction": "start", "along": [0, 1], "id": 3}]}, {"panel": "up_back", "edge_list": [{"direction": "end", "along": [0, 1], "id": 7}, {"direction": "start", "along": [0, 1], "id": 3}]}], "value": 1.9397967978286617}}, "parameter_order": ["collar_wide", "fcollar_depth", "bcollar_depth", "top_wideness", "waist_wideness", "sldepth", "leg_length", "leg_wideness"], "constraints": {}, "constraint_order": []}